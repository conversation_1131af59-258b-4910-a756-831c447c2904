import { Connection, RpcResponseAndContext, SignatureStatus } from '@solana/web3.js';

export async function waitForConfirmation(
  connection: Connection,
  signatures: string[],
  timeoutMs: number = 30000
): Promise<(null | SignatureStatus)[]> {
  const startTime = Date.now();
  let latestStatuses: (null | SignatureStatus)[] = [];

  return new Promise((resolve, reject) => {
    const checkStatus = async () => {
      while (Date.now() - startTime < timeoutMs) {
        try {
          const response: RpcResponseAndContext<(SignatureStatus | null)[]> =
            await connection.getSignatureStatuses(signatures);
          latestStatuses = response.value;

          console.log('检查交易状态:', latestStatuses);

          // 检查签名是否已确认
          if (
            latestStatuses.every(
              (status) =>
                status &&
                !status.err &&
                (status.confirmationStatus === 'confirmed' || status.confirmationStatus === 'finalized')
            )
          ) {
            console.log('所有交易已确认');
            return resolve(latestStatuses);
          }

          // 检查是否有错误
          const hasError = latestStatuses.some((status) => status?.err);
          if (hasError) {
            console.error('交易出现错误:', latestStatuses);
            return resolve(latestStatuses);
          }

          // 等待500毫秒后再次检查
          await new Promise((r) => setTimeout(r, 500));
        } catch (error) {
          console.error('检查交易状态时出错:', error);
          await new Promise((r) => setTimeout(r, 1000));
        }
      }

      console.log('交易确认超时，最后状态:', latestStatuses);
      resolve(latestStatuses);
    };

    checkStatus().catch((error) => {
      console.error('交易确认检查失败:', error);
      reject(error);
    });
  });
}