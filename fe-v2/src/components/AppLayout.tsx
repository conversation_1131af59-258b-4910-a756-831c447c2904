import React from 'react';
import { Layout, Menu } from 'antd';
import { useLocation, history } from '@umijs/max';
import { UserOutlined, SendOutlined, FileTextOutlined, TeamOutlined, SettingOutlined } from '@ant-design/icons';
import { CustomHeader } from './CustomHeader';

const { Sider, Content } = Layout;

interface AppLayoutProps {
  children: React.ReactNode;
}

export const AppLayout: React.FC<AppLayoutProps> = ({ children }) => {
  const location = useLocation();

  const menuItems = [
    {
      key: '/account',
      icon: <UserOutlined />,
      label: 'Account',
    },
    {
      key: '/send',
      icon: <SendOutlined />,
      label: 'Send',
    },
    {
      key: '/transactions',
      icon: <FileTextOutlined />,
      label: 'Transactions',
    },
    {
      key: '/members',
      icon: <TeamOutlined />,
      label: 'Members',
    },
    {
      key: '/settings',
      icon: <SettingOutlined />,
      label: 'Settings',
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    history.push(key);
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider
        width={208}
        style={{
          background: '#fff',
          boxShadow: '2px 0 8px rgba(0,0,0,0.15)'
        }}
      >
        <div style={{
          height: '64px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          borderBottom: '1px solid #f0f0f0',
          fontSize: '18px',
          fontWeight: 'bold',
          color: '#1890ff'
        }}>
          SQUADS
        </div>
        <Menu
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
          style={{
            border: 'none',
            marginTop: '16px'
          }}
        />
      </Sider>
      <Layout>
        <CustomHeader />
        <Content style={{ background: '#f5f5f5' }}>
          {children}
        </Content>
      </Layout>
    </Layout>
  );
};