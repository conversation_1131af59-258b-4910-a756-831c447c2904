import React, { useState, useEffect } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { WalletMultiButton } from '@solana/wallet-adapter-react-ui';

interface CustomHeaderProps {
  title?: string;
}

export const CustomHeader: React.FC<CustomHeaderProps> = () => {
  const { publicKey, connected } = useWallet();
  const [balance, setBalance] = useState<number | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (connected && publicKey) {
      const fetchBalance = async () => {
        try {
          setLoading(true);
          const response = await fetch('http://localhost:3001/api/account', {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              publicKey: publicKey.toBase58()
            })
          });
          if (response.ok) {
            const data = await response.json();
            const balanceInSOL = data.balance ? data.balance / 1e9 : 0;
            setBalance(balanceInSOL);
          } else {
            setBalance(0.0140);
          }
        } catch (error) {
          setBalance(0.0140);
        } finally {
          setLoading(false);
        }
      };
      fetchBalance();
      const interval = setInterval(fetchBalance, 30000);
      return () => clearInterval(interval);
    } else {
      setBalance(null);
    }
  }, [connected, publicKey]);

  const displayBalance = () => {
    if (loading) return '加载中...';
    if (connected && balance !== null) {
      return `${balance.toFixed(4)} SOL`;
    }
    return '0.0000 SOL';
  };

  return (
    <div
      style={{
        background: '#fff',
        padding: '16px 24px',
        display: 'flex',
        justifyContent: 'flex-end',
        alignItems: 'center',
        borderBottom: '1px solid #f0f0f0',
        boxShadow: '0 1px 4px rgba(0,0,0,0.1)'
      }}
    >
      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: '24px'
      }}>
        {/* SOL 余额显示 */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          background: '#f8f9fa',
          padding: '8px 20px 8px 12px',
          borderRadius: '12px',
          border: '1px solid #e9ecef',
          fontWeight: 600
        }}>
          <div style={{
            width: '24px',
            height: '24px',
            background: '#000',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            marginRight: 4
          }}>
            <span style={{
              color: '#fff',
              fontSize: '13px',
              fontWeight: 'bold'
            }}>
              ◎
            </span>
          </div>
          <span style={{
            fontSize: '16px',
            fontWeight: '700',
            color: connected ? '#000' : '#666',
            letterSpacing: 1
          }}>
            {displayBalance()}
          </span>
        </div>
        {/* 钱包连接按钮（自带地址和图标） */}
        <WalletMultiButton style={{ height: 48, borderRadius: 12, fontWeight: 700, fontSize: 18 }} />
      </div>
    </div>
  );
};