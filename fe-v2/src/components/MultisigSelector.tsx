import React, { useState } from 'react';
import { useMultisigAccounts } from '@/hooks/useMultisigAccounts';
import { RefreshCw, Plus, Users, Shield } from 'lucide-react';

interface MultisigAccount {
  address: string;
  members: number;
  threshold: number;
  transactionIndex: number;
}

interface MultisigSelectorProps {
  selectedMultisig: string;
  onSelect: (address: string) => void;
  onCreateNew?: () => void;
}

const MultisigSelector: React.FC<MultisigSelectorProps> = ({
  selectedMultisig,
  onSelect,
  onCreateNew
}) => {
  const { multisigs, loading, error } = useMultisigAccounts();
  const [isOpen, setIsOpen] = useState(false);

  const renderMultisigItem = (account: MultisigAccount) => (
    <div
      key={account.address}
      className={`p-3 border rounded-lg cursor-pointer transition-colors ${
        selectedMultisig === account.address
          ? 'border-blue-500 bg-blue-50'
          : 'border-gray-200 hover:border-gray-300'
      }`}
      onClick={() => {
        onSelect(account.address);
        setIsOpen(false);
      }}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Users size={16} className="text-gray-500" />
          <span className="font-medium text-sm">
            {account.address.slice(0, 8)}...{account.address.slice(-4)}
          </span>
        </div>
        <div className="flex items-center gap-2 text-xs text-gray-500">
          <Shield size={12} />
          <span>{account.threshold}/{account.members}</span>
        </div>
      </div>
      <div className="text-xs text-gray-400 mt-1">
        交易索引: {account.transactionIndex}
      </div>
    </div>
  );

  return (
    <div className="mb-4">
      <label className="block text-sm font-medium text-gray-700 mb-2">
        选择多签账户
      </label>

      {loading && (
        <div className="flex items-center gap-2 text-gray-500 text-sm">
          <RefreshCw size={16} className="animate-spin" />
          正在加载多签账户...
        </div>
      )}

      {error && (
        <div className="text-red-500 text-sm mb-2">
          {error}
        </div>
      )}

      {!loading && !error && (
        <>
          {multisigs.length === 0 ? (
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <Users size={48} className="mx-auto text-gray-400 mb-2" />
              <p className="text-gray-500 mb-2">未找到多签账户</p>
              <p className="text-sm text-gray-400 mb-4">
                您的钱包不是任何多签账户的成员
              </p>
            </div>
          ) : (
            <div className="space-y-2">
              {multisigs.map(renderMultisigItem)}
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default MultisigSelector;