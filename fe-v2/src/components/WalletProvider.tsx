import React from 'react';
import { ConnectionProvider, WalletProvider } from '@solana/wallet-adapter-react';
import { WalletAdapterNetwork } from '@solana/wallet-adapter-base';
import { WalletModalProvider } from '@solana/wallet-adapter-react-ui';
import { PhantomWalletAdapter } from '@solana/wallet-adapter-phantom';
import { clusterApiUrl } from '@solana/web3.js';

// 引入钱包样式
import '@solana/wallet-adapter-react-ui/styles.css';

// 选择网络
const network = WalletAdapterNetwork.Mainnet;
const endpoint = clusterApiUrl(network);

console.log('Solana 网络配置:', { network, endpoint });

// 初始化钱包适配器
const wallets = [
  new PhantomWalletAdapter(),
];

interface Props {
  children: React.ReactNode;
}

export const SolanaWalletProvider: React.FC<Props> = ({ children }) => {
  return (
    <ConnectionProvider endpoint={endpoint}>
      <WalletProvider wallets={wallets} autoConnect>
        <WalletModalProvider>
          {children}
        </WalletModalProvider>
      </WalletProvider>
    </ConnectionProvider>
  );
};