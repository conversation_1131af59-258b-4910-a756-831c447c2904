import React, { useState, useEffect } from 'react';
import { Coins, RefreshCw } from 'lucide-react';
import { apiService, TokenAccount } from '@/services/ApiService';

interface TokenAssetsProps {
  multisigAddress?: string;
  onError?: (error: string) => void;
}

const TokenAssets: React.FC<TokenAssetsProps> = ({ multisigAddress, onError }) => {
  const [tokens, setTokens] = useState<TokenAccount[]>([]);
  const [loading, setLoading] = useState(false);

  const loadTokens = async () => {
    try {
      setLoading(true);
      
      if (multisigAddress) {
        // 获取指定多签的 Token 资产
        const tokenList = await apiService.getMultisigTokens(multisigAddress);
        setTokens(tokenList);
      } else {
        // 获取所有多签的 Token 资产
        const allTokens = await apiService.getAllTokens();
        const flatTokens = allTokens.flatMap(item => item.tokens);
        setTokens(flatTokens);
      }
    } catch (error: any) {
      console.error('获取 Token 资产失败:', error);
      onError?.(error.message || '获取 Token 资产失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadTokens();
  }, [multisigAddress]);

  const formatTokenAmount = (token: TokenAccount) => {
    return `${token.uiAmountString} ${getTokenSymbol(token.mint)}`;
  };

  const getTokenSymbol = (mint: string) => {
    // 常见 Token 的符号映射
    const tokenSymbols: { [key: string]: string } = {
      'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v': 'USDC',
      'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB': 'USDT',
      'So11111111111111111111111111111111111111112': 'SOL',
      'mSoLzYCxHdYgdzU16g5QSh3i5K3z3KZK7ytfqcJm7So': 'mSOL',
    };
    return tokenSymbols[mint] || `${mint.substring(0, 4)}...${mint.substring(mint.length - 4)}`;
  };

  const formatAddress = (address: string) => {
    return `${address.substring(0, 8)}...${address.substring(address.length - 4)}`;
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-center py-8">
          <RefreshCw className="w-6 h-6 animate-spin text-blue-500 mr-2" />
          <span className="text-gray-600">加载 Token 资产中...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow">
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Coins className="w-5 h-5 text-green-500 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">
              Token 资产 {multisigAddress && `(${formatAddress(multisigAddress)})`}
            </h3>
          </div>
          <button
            onClick={loadTokens}
            disabled={loading}
            className="flex items-center px-3 py-1 text-sm text-blue-600 hover:text-blue-800 disabled:opacity-50"
          >
            <RefreshCw className={`w-4 h-4 mr-1 ${loading ? 'animate-spin' : ''}`} />
            刷新
          </button>
        </div>
      </div>

      <div className="p-6">
        {tokens.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Coins className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <p>暂无 Token 资产</p>
          </div>
        ) : (
          <div className="space-y-4">
            {tokens.map((token, index) => (
              <div key={`${token.address}-${index}`} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium text-gray-900">
                      {getTokenSymbol(token.mint)}
                    </span>
                    <span className="text-lg font-semibold text-green-600">
                      {formatTokenAmount(token)}
                    </span>
                  </div>
                  <div className="text-sm text-gray-500 space-y-1">
                    <div>账户: {formatAddress(token.address)}</div>
                    <div>Mint: {formatAddress(token.mint)}</div>
                    <div>精度: {token.decimals} 位</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default TokenAssets;
