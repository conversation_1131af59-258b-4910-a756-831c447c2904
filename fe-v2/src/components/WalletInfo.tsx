import React, { useState, useEffect } from 'react';
import { Tag } from 'antd';
import { useWallet, useConnection } from '@solana/wallet-adapter-react';
import { WalletMultiButton } from '@solana/wallet-adapter-react-ui';
import { LAMPORTS_PER_SOL } from '@solana/web3.js';

export const WalletInfo: React.FC = () => {
  const { publicKey, connected } = useWallet();
  const { connection } = useConnection();
  const [balance, setBalance] = useState<number | null>(null);

  useEffect(() => {
    if (connected && publicKey) {
      const fetchBalance = async () => {
        try {
          const balance = await connection.getBalance(publicKey);
          setBalance(balance / LAMPORTS_PER_SOL);
        } catch (error) {
          console.error('获取余额失败:', error);
          setBalance(0);
        }
      };

      fetchBalance();
      const interval = setInterval(fetchBalance, 10000);
      return () => clearInterval(interval);
    } else {
      setBalance(null);
    }
  }, [connected, publicKey, connection]);

  return (
    <div style={{
      display: 'flex',
      alignItems: 'center',
      gap: '12px',
      padding: '0 16px',
      height: '48px'
    }}>
      <Tag color="green">Network Status</Tag>

      {connected && balance !== null && (
        <Tag color="blue">{balance.toFixed(4)} SOL</Tag>
      )}

      {connected && balance !== null && balance < 0.01 && (
        <Tag color="orange">⚠</Tag>
      )}

      {connected && publicKey && (
        <Tag>
          {publicKey.toBase58().slice(0, 4)}...{publicKey.toBase58().slice(-4)}
        </Tag>
      )}

      <WalletMultiButton style={{ height: '32px' }} />
    </div>
  );
};