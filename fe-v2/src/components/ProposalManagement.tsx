import React, { useState, useCallback, useEffect, useRef } from 'react';
import { useWallet, useConnection } from '@solana/wallet-adapter-react';
import { PublicKey, TransactionMessage, VersionedTransaction } from '@solana/web3.js';
import * as multisig from '@sqds/multisig';
import { Vote, CheckCircle, XCircle, Clock, Play, RefreshCw, AlertCircle } from 'lucide-react';
import MultisigSelector from './MultisigSelector';
import { apiService, Transaction } from '@/services/ApiService';

interface ProposalManagementProps {
  onSuccess?: (signature: string) => void;
  onError?: (error: string) => void;
}

const ProposalManagement: React.FC<ProposalManagementProps> = ({ onSuccess, onError }) => {
  const { publicKey, signTransaction } = useWallet();
  const { connection } = useConnection();
  const [selectedMultisig, setSelectedMultisig] = useState('');
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(false);
  const [actionLoading, setActionLoading] = useState<string>('');
  const [status, setStatus] = useState<string>('');

  // 使用useRef存储回调函数，避免依赖变化
  const onErrorRef = useRef(onError);
  const onSuccessRef = useRef(onSuccess);

  // 更新ref
  useEffect(() => {
    onErrorRef.current = onError;
    onSuccessRef.current = onSuccess;
  }, [onError, onSuccess]);

  // 默认配置
  const programId = new PublicKey("SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf");

  const handleCreateNew = () => {
    onErrorRef.current?.('创建新多签账户功能将在后续步骤中实现');
  };

  const loadTransactions = useCallback(async () => {
    if (!selectedMultisig || !publicKey) return;

    setLoading(true);
    setStatus('正在加载交易...');

    try {
      const transactionData = await apiService.getTransactions(selectedMultisig);
      setTransactions(transactionData.transactions);
      setStatus('');
    } catch (error: any) {
      console.error('加载交易失败:', error);
      onErrorRef.current?.(error.message || '加载交易失败');
      setTransactions([]);
    } finally {
      setLoading(false);
    }
  }, [selectedMultisig, publicKey]);

  useEffect(() => {
    loadTransactions();
  }, [loadTransactions]);

  const handleVote = useCallback(async (transactionIndex: number, vote: 'approve' | 'reject') => {
    if (!signTransaction || !publicKey) {
      onErrorRef.current?.('钱包不支持签名功能');
      return;
    }

    const actionKey = `${vote}-${transactionIndex}`;
    setActionLoading(actionKey);
    setStatus(`正在${vote === 'approve' ? '批准' : '拒绝'}交易 #${transactionIndex}...`);

    try {
      const multisigPda = new PublicKey(selectedMultisig);
      const { blockhash } = await apiService.getBlockhash();

      let instruction;
      if (vote === 'approve') {
        instruction = multisig.instructions.proposalApprove({
          multisigPda,
          transactionIndex: BigInt(transactionIndex),
          member: publicKey,
          programId,
        });
      } else {
        instruction = multisig.instructions.proposalReject({
          multisigPda,
          transactionIndex: BigInt(transactionIndex),
          member: publicKey,
          programId,
        });
      }

      const message = new TransactionMessage({
        payerKey: publicKey,
        recentBlockhash: blockhash,
        instructions: [instruction],
      });

      const transaction = new VersionedTransaction(message.compileToV0Message());
      const signedTx = await signTransaction(transaction);

      const result = await apiService.submitVote({
        multisigAddress: selectedMultisig,
        transactionIndex: transactionIndex,
        userPublicKey: publicKey.toBase58(),
        vote: vote,
        signedTransaction: Buffer.from(signedTx.serialize()).toString('base64')
      });

      setStatus(`交易 #${transactionIndex} ${vote === 'approve' ? '批准' : '拒绝'}成功！`);
      onSuccessRef.current?.(result.signature);

      // 重新加载交易
      setTimeout(() => {
        loadTransactions();
        setStatus('');
      }, 2000);

    } catch (error: any) {
      console.error('投票失败:', error);
      setStatus('');
      onErrorRef.current?.(error.message || '投票失败');
    } finally {
      setActionLoading('');
    }
  }, [publicKey, signTransaction, selectedMultisig, loadTransactions]);

  const handleExecute = useCallback(async (transactionIndex: number) => {
    if (!signTransaction || !publicKey) {
      onErrorRef.current?.('钱包不支持签名功能');
      return;
    }

    const actionKey = `execute-${transactionIndex}`;
    setActionLoading(actionKey);
    setStatus(`正在执行交易 #${transactionIndex}...`);

    try {
      console.log('开始执行交易:', transactionIndex);
      console.log('publicKey:', publicKey?.toBase58());
      console.log('selectedMultisig:', selectedMultisig);

      const multisigPda = new PublicKey(selectedMultisig);

      // 使用后端API获取区块哈希，确保使用正确的网络
      console.log('获取区块哈希...');
      const { blockhash } = await apiService.getBlockhash();
      console.log('获取到区块哈希:', blockhash);

      setStatus(`构建执行指令...`);
      console.log('通过后端API构建执行指令...');

      // 使用后端API构建执行指令
      const instructionData = await apiService.buildExecuteInstruction({
        multisigAddress: selectedMultisig,
        transactionIndex: transactionIndex,
        executorPublicKey: publicKey.toBase58(),
      });

      console.log('执行指令构建成功:', instructionData);

      // 重构指令数据
      const instruction = {
        keys: instructionData.instruction.keys.map(key => ({
          pubkey: new PublicKey(key.pubkey),
          isSigner: key.isSigner,
          isWritable: key.isWritable,
        })),
        programId: new PublicKey(instructionData.instruction.programId),
        data: Buffer.from(instructionData.instruction.data),
      };

      const message = new TransactionMessage({
        payerKey: publicKey,
        recentBlockhash: blockhash,
        instructions: [instruction],
      });

      console.log('创建TransactionMessage成功');

      const transaction = new VersionedTransaction(message.compileToV0Message());
      console.log('交易创建成功');

      setStatus(`请在钱包中签名...`);
      console.log('请求钱包签名...');
      const signedTx = await signTransaction(transaction);
      console.log('钱包签名成功');

      // 检查签名结果
      if (!signedTx) {
        throw new Error('钱包签名失败，返回值为空');
      }

      setStatus(`提交执行交易...`);
      console.log('提交执行交易到后端...');
      const result = await apiService.executeTransaction({
        multisigAddress: selectedMultisig,
        transactionIndex: transactionIndex,
        userPublicKey: publicKey.toBase58(),
        signedTransaction: Buffer.from(signedTx.serialize()).toString('base64')
      });
      console.log('执行交易成功:', result);

      setStatus(`交易 #${transactionIndex} 执行成功！`);
      onSuccessRef.current?.(result.signature);

      // 重新加载交易
      setTimeout(() => {
        loadTransactions();
        setStatus('');
      }, 2000);

    } catch (error: any) {
      console.error('执行失败:', error);
      setStatus('');

      // 提供更详细的错误信息
      let errorMessage = error.message || '执行失败';
      if (error.message?.includes('没有对应的交易内容')) {
        errorMessage = `${error.message}\n\n建议：请尝试创建新的转账交易，旧的交易可能无法执行。`;
      } else if (error.message?.includes('Access forbidden') || error.message?.includes('403')) {
        errorMessage = '网络访问受限，请检查网络连接或稍后重试。';
      }

      onErrorRef.current?.(errorMessage);
    } finally {
      setActionLoading('');
    }
  }, [publicKey, signTransaction, selectedMultisig, loadTransactions]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Executed': return 'text-green-600 bg-green-50';
      case 'Approved': return 'text-blue-600 bg-blue-50';
      case 'Active': return 'text-yellow-600 bg-yellow-50';
      case 'Rejected': return 'text-red-600 bg-red-50';
      case 'Cancelled': return 'text-gray-600 bg-gray-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Executed': return <CheckCircle className="w-4 h-4" />;
      case 'Approved': return <Play className="w-4 h-4" />;
      case 'Active': return <Clock className="w-4 h-4" />;
      case 'Rejected': return <XCircle className="w-4 h-4" />;
      case 'Cancelled': return <AlertCircle className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  const formatAddress = (address: string, length: number = 8) => {
    if (!address) return '';
    return `${address.substring(0, length)}...${address.substring(address.length - 4)}`;
  };

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-4">交易管理</h2>

        <div className="mb-6">
          <MultisigSelector
            selectedMultisig={selectedMultisig}
            onSelect={setSelectedMultisig}
            onCreateNew={handleCreateNew}
          />
        </div>

        {status && (
          <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
            <p className="text-blue-700 text-sm">{status}</p>
          </div>
        )}

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="w-6 h-6 animate-spin text-blue-500 mr-2" />
            <span className="text-gray-600">加载中...</span>
          </div>
        ) : transactions.length === 0 ? (
          <div className="text-center py-8">
            <Vote className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">暂无交易</h3>
            <p className="text-gray-500">
              {selectedMultisig ? '当前多签账户没有待处理的交易' : '请选择一个多签账户'}
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">
                交易列表 ({transactions.length})
              </h3>
              <button
                onClick={loadTransactions}
                disabled={loading}
                className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
                <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                刷新
              </button>
            </div>

            {transactions.map((transaction) => (
              <div
                key={transaction.transactionIndex}
                className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <span className="text-lg font-medium text-gray-900">
                      #{transaction.transactionIndex}
                    </span>
                    <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(transaction.status)}`}>
                      {getStatusIcon(transaction.status)}
                      <span className="ml-1">{transaction.status}</span>
                    </div>
                  </div>
                  <div className="text-sm text-gray-500">
                    投票: {transaction.approvals}/{transaction.threshold}
                  </div>
                </div>

                {transaction.memo && (
                  <p className="text-sm text-gray-600 mb-3">{transaction.memo}</p>
                )}

                <div className="flex items-center justify-between text-sm text-gray-500 mb-3">
                  <span>创建者: {formatAddress(transaction.creator)}</span>
                </div>

                {transaction.votes.length > 0 && (
                  <div className="mb-3">
                    <p className="text-sm font-medium text-gray-700 mb-2">投票记录:</p>
                    <div className="flex flex-wrap gap-2">
                      {transaction.votes.map((vote, index) => (
                        <span
                          key={index}
                          className={`inline-flex items-center px-2 py-1 rounded-md text-xs ${
                            vote.vote === 'Approve'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800'
                          }`}
                        >
                          {formatAddress(vote.member, 6)} - {vote.vote}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                <div className="flex space-x-2">
                  {transaction.status === 'Active' && (
                    <>
                      <button
                        onClick={() => handleVote(transaction.transactionIndex, 'approve')}
                        disabled={!!actionLoading}
                        className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
                      >
                        {actionLoading === `approve-${transaction.transactionIndex}` ? (
                          <RefreshCw className="w-4 h-4 animate-spin mr-2" />
                        ) : (
                          <CheckCircle className="w-4 h-4 mr-2" />
                        )}
                        批准
                      </button>
                      <button
                        onClick={() => handleVote(transaction.transactionIndex, 'reject')}
                        disabled={!!actionLoading}
                        className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                      >
                        {actionLoading === `reject-${transaction.transactionIndex}` ? (
                          <RefreshCw className="w-4 h-4 animate-spin mr-2" />
                        ) : (
                          <XCircle className="w-4 h-4 mr-2" />
                        )}
                        拒绝
                      </button>
                    </>
                  )}

                  {transaction.canExecute && transaction.status === 'Approved' && (
                    <button
                      onClick={() => handleExecute(transaction.transactionIndex)}
                      disabled={!!actionLoading}
                      className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                    >
                      {actionLoading === `execute-${transaction.transactionIndex}` ? (
                        <RefreshCw className="w-4 h-4 animate-spin mr-2" />
                      ) : (
                        <Play className="w-4 h-4 mr-2" />
                      )}
                      执行
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default ProposalManagement;