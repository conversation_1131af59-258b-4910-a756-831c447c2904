// 运行时配置
import './polyfills';
import React from 'react';
import { SolanaWalletProvider } from './components/WalletProvider';

// 全局初始化数据配置，用于 Layout 用户信息和权限初始化
// 更多信息见文档：https://umijs.org/docs/api/runtime-config#getinitialstate
export async function getInitialState(): Promise<{ name: string }> {
  return { name: 'Squads Network' };
}

// 全局包装器，为整个应用提供钱包上下文
export function rootContainer(container: React.ReactElement) {
  return React.createElement(SolanaWalletProvider, {}, container);
}
