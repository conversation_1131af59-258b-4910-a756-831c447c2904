import { useState, useEffect } from 'react';
import { apiService, MultisigInfo } from '@/services/ApiService';

export const useMultisigAccounts = () => {
  const [multisigs, setMultisigs] = useState<MultisigInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchMultisigs = async () => {
      setLoading(true);
      setError(null);

      try {
        const result = await apiService.getMultisigs();
        setMultisigs(result.multisigs);
      } catch (err: any) {
        console.error('获取多签账户失败:', err);
        setError(err.message || '获取多签账户失败');
        setMultisigs([]);
      } finally {
        setLoading(false);
      }
    };

    fetchMultisigs();
  }, []);

  return { multisigs, loading, error };
};