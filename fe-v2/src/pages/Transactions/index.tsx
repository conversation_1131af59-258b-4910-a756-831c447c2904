import React from 'react';
import { Card, message } from 'antd';
import ProposalManagement from '@/components/ProposalManagement';

const TransactionsPage: React.FC = () => {
  const [messageApi, contextHolder] = message.useMessage();

  const handleSuccess = (text: string) => {
    messageApi.success(text);
  };

  const handleError = (text: string) => {
    messageApi.error(text);
  };

  return (
    <>
      {contextHolder}
      <div style={{ padding: '24px' }}>
        <Card>
          <ProposalManagement onSuccess={handleSuccess} onError={handleError} />
        </Card>
      </div>
    </>
  );
};

export default TransactionsPage;