import React, { useEffect, useState } from 'react';
import { AppLayout } from '@/components/AppLayout';
import { Table, Avatar, Button, message, Typography, Space } from 'antd';
import { CopyOutlined } from '@ant-design/icons';
import { useWallet } from '@solana/wallet-adapter-react';

const { Text } = Typography;

interface CoinAsset {
  symbol: string;
  icon: string;
  address: string;
  balance: number;
  value: number;
  weight: number;
}

const shortAddr = (addr: string) => addr.slice(0, 4) + '...' + addr.slice(-4);

const AccountPage: React.FC = () => {
  const { publicKey, connected } = useWallet();
  const [assets, setAssets] = useState<CoinAsset[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchAssets = async () => {
      if (!connected || !publicKey) {
        setAssets([]);
        return;
      }

      setLoading(true);
      try {
        const res = await fetch('http://localhost:3001/api/account', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ publicKey: publicKey.toBase58() })
        });

        if (!res.ok) {
          throw new Error('请求失败');
        }

        const data = await res.json();

        // 构造资产列表
        const solAsset = {
          symbol: 'SOL',
          icon: 'https://cryptologos.cc/logos/solana-sol-logo.png',
          address: 'So11111111111111111111111111111111111111112',
          balance: data.balance / 1e9, // 转换为 SOL 单位
          value: (data.balance / 1e9) * 200, // 假设 SOL 价格 200 USD
          weight: 1, // 暂时写死权重
        };

        setAssets([solAsset]);
      } catch (e) {
        message.error('获取资产失败');
      } finally {
        setLoading(false);
      }
    };
    fetchAssets();
  }, [connected, publicKey]);

  const columns = [
    {
      title: 'Coin',
      dataIndex: 'symbol',
      key: 'symbol',
      render: (_: any, record: CoinAsset) => (
        <Space>
          <Avatar src={record.icon} size={40} />
          <div>
            <div style={{ fontWeight: 700 }}>{record.symbol}</div>
            <Space size={4}>
              <Text type="secondary" style={{ fontSize: 13 }}>{shortAddr(record.address)}</Text>
              <Button
                type="text"
                size="small"
                icon={<CopyOutlined />}
                onClick={() => {
                  navigator.clipboard.writeText(record.address);
                  message.success('已复制');
                }}
              />
            </Space>
          </div>
        </Space>
      )
    },
    {
      title: 'Balance',
      dataIndex: 'balance',
      key: 'balance',
      render: (v: number) => <span style={{ fontWeight: 700 }}>{v.toFixed(4)}</span>
    },
    {
      title: 'Value',
      dataIndex: 'value',
      key: 'value',
      render: (v: number) => <span style={{ fontWeight: 700 }}>${v.toFixed(2)}</span>
    },
    {
      title: 'Weight',
      dataIndex: 'weight',
      key: 'weight',
      render: (v: number) => (
        <div style={{ minWidth: 80 }}>
          <div style={{ fontWeight: 700 }}>{Math.round(v * 100)}%</div>
          <div style={{ background: '#eee', borderRadius: 4, height: 6, width: 60, marginTop: 2 }}>
            <div style={{ width: `${v * 100}%`, height: '100%', background: '#111' }}></div>
          </div>
        </div>
      )
    }
  ];

  return (
    <AppLayout>
      <div style={{ maxWidth: 900, margin: '0 auto', padding: '32px 0' }}>
        <Table
          columns={columns}
          dataSource={assets}
          rowKey={r => r.symbol + r.address}
          loading={loading}
          pagination={false}
          bordered
          style={{ background: '#fff', borderRadius: 16 }}
        />
      </div>
    </AppLayout>
  );
};

export default AccountPage;