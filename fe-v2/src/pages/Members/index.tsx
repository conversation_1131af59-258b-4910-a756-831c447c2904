import React from 'react';
import { Card, message } from 'antd';
import MemberManagement from '@/components/MemberManagement';

const MembersPage: React.FC = () => {
  const [messageApi, contextHolder] = message.useMessage();

  const handleError = (text: string) => {
    messageApi.error(text);
  };

  return (
    <>
      {contextHolder}
      <div style={{ padding: '24px' }}>
        <Card>
          <MemberManagement onError={handleError} />
        </Card>
      </div>
    </>
  );
};

export default MembersPage;