import React, { useState, useEffect } from 'react';
import { Card, Descriptions, Badge, Button, message, Spin, Alert } from 'antd';
import { 
  SettingOutlined, 
  ReloadOutlined, 
  TeamOutlined, 
  WalletOutlined,
  KeyOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { apiService, MultisigInfo } from '@/services/ApiService';

const Settings: React.FC = () => {
  const [multisigs, setMultisigs] = useState<MultisigInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadMultisigSettings = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await apiService.getMultisigs();
      setMultisigs(result.multisigs);
      
      if (result.multisigs.length === 0) {
        message.warning('未找到配置的多签账户');
      } else {
        message.success(`成功加载 ${result.multisigs.length} 个多签账户配置`);
      }
    } catch (err: any) {
      console.error('加载多签配置失败:', err);
      setError(err.message || '加载多签配置失败');
      message.error('加载多签配置失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadMultisigSettings();
  }, []);

  const formatAddress = (address: string, length: number = 8) => {
    if (!address) return '';
    return `${address.substring(0, length)}...${address.substring(address.length - 4)}`;
  };

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text).then(() => {
      message.success(`${label} 已复制到剪贴板`);
    }).catch(() => {
      message.error('复制失败');
    });
  };

  const getPermissionText = (mask: number) => {
    const permissions = [];
    if (mask & 1) permissions.push('Initiate');
    if (mask & 2) permissions.push('Vote');
    if (mask & 4) permissions.push('Execute');
    return permissions.join(', ') || 'None';
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <Spin size="large" />
        <span className="ml-3 text-lg">加载多签配置中...</span>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <SettingOutlined className="text-2xl text-blue-500 mr-3" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">系统设置</h1>
              <p className="text-gray-600 mt-1">多签账户配置信息</p>
            </div>
          </div>
          <Button 
            type="primary" 
            icon={<ReloadOutlined />} 
            onClick={loadMultisigSettings}
            loading={loading}
          >
            刷新配置
          </Button>
        </div>
      </div>

      {error && (
        <Alert
          message="加载失败"
          description={error}
          type="error"
          showIcon
          className="mb-6"
          action={
            <Button size="small" onClick={loadMultisigSettings}>
              重试
            </Button>
          }
        />
      )}

      {multisigs.length === 0 && !loading && !error && (
        <Alert
          message="未找到多签配置"
          description="请检查后端配置文件中的 KNOWN_MULTISIG_ADDRESSES 设置"
          type="warning"
          showIcon
          className="mb-6"
        />
      )}

      <div className="space-y-6">
        {multisigs.map((multisig, index) => (
          <Card
            key={multisig.address}
            title={
              <div className="flex items-center">
                <WalletOutlined className="text-blue-500 mr-2" />
                <span>多签账户 #{index + 1}</span>
                <Badge 
                  count={`${multisig.members.length} 成员`} 
                  style={{ backgroundColor: '#52c41a', marginLeft: 12 }}
                />
              </div>
            }
            extra={
              <Button 
                type="link" 
                onClick={() => copyToClipboard(multisig.address, '多签地址')}
              >
                复制地址
              </Button>
            }
            className="shadow-sm"
          >
            <Descriptions column={2} bordered size="small">
              <Descriptions.Item 
                label={
                  <span className="flex items-center">
                    <WalletOutlined className="mr-1" />
                    多签地址
                  </span>
                }
                span={2}
              >
                <div className="flex items-center justify-between">
                  <code className="bg-gray-100 px-2 py-1 rounded text-sm">
                    {multisig.address}
                  </code>
                  <Button 
                    type="link" 
                    size="small"
                    onClick={() => copyToClipboard(multisig.address, '多签地址')}
                  >
                    复制
                  </Button>
                </div>
              </Descriptions.Item>

              <Descriptions.Item 
                label={
                  <span className="flex items-center">
                    <TeamOutlined className="mr-1" />
                    成员数量
                  </span>
                }
              >
                <Badge count={multisig.members.length} style={{ backgroundColor: '#1890ff' }} />
              </Descriptions.Item>

              <Descriptions.Item 
                label={
                  <span className="flex items-center">
                    <KeyOutlined className="mr-1" />
                    签名阈值
                  </span>
                }
              >
                <Badge count={multisig.threshold} style={{ backgroundColor: '#f50' }} />
              </Descriptions.Item>

              <Descriptions.Item 
                label={
                  <span className="flex items-center">
                    <WalletOutlined className="mr-1" />
                    金库地址
                  </span>
                }
                span={2}
              >
                <div className="flex items-center justify-between">
                  <code className="bg-gray-100 px-2 py-1 rounded text-sm">
                    {multisig.vault.address}
                  </code>
                  <Button 
                    type="link" 
                    size="small"
                    onClick={() => copyToClipboard(multisig.vault.address, '金库地址')}
                  >
                    复制
                  </Button>
                </div>
              </Descriptions.Item>

              <Descriptions.Item label="SOL 余额">
                <span className="text-green-600 font-medium">
                  {multisig.vault.balanceSOL.toFixed(6)} SOL
                </span>
              </Descriptions.Item>

              <Descriptions.Item label="Token 资产">
                <span className="text-blue-600 font-medium">
                  {multisig.vault.tokenAccounts.length} 种
                </span>
              </Descriptions.Item>

              <Descriptions.Item label="交易索引">
                {multisig.transactionIndex}
              </Descriptions.Item>

              <Descriptions.Item label="外部执行">
                <Badge 
                  status={multisig.allowExternalExecute ? "success" : "default"} 
                  text={multisig.allowExternalExecute ? "允许" : "禁止"} 
                />
              </Descriptions.Item>

              <Descriptions.Item 
                label={
                  <span className="flex items-center">
                    <InfoCircleOutlined className="mr-1" />
                    创建者
                  </span>
                }
                span={2}
              >
                <code className="bg-gray-100 px-2 py-1 rounded text-sm">
                  {formatAddress(multisig.createKey, 12)}
                </code>
              </Descriptions.Item>
            </Descriptions>

            {/* 成员列表 */}
            <div className="mt-4">
              <h4 className="text-lg font-medium mb-3 flex items-center">
                <TeamOutlined className="mr-2" />
                成员列表
              </h4>
              <div className="space-y-2">
                {multisig.members.map((member, memberIndex) => (
                  <div 
                    key={member.key} 
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                  >
                    <div className="flex items-center">
                      <Badge 
                        count={memberIndex + 1} 
                        style={{ backgroundColor: '#722ed1', marginRight: 12 }}
                      />
                      <code className="text-sm">
                        {formatAddress(member.key, 12)}
                      </code>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-600">
                        权限: {getPermissionText(member.permissions.mask)}
                      </span>
                      <Button 
                        type="link" 
                        size="small"
                        onClick={() => copyToClipboard(member.key, `成员 ${memberIndex + 1} 地址`)}
                      >
                        复制
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default Settings;
