import React from 'react';
import { Card, Tabs, message } from 'antd';
import { AppLayout } from '@/components/AppLayout';
import SolTransfer from '@/components/SolTransfer';
import TokenTransfer from '@/components/TokenTransfer';

const SendPage: React.FC = () => {
  const [messageApi, contextHolder] = message.useMessage();

  const handleSuccess = (text: string) => {
    messageApi.success(text);
  };

  const handleError = (text: string) => {
    messageApi.error(text);
  };

  const items = [
    {
      key: 'sol',
      label: 'SOL 转账',
      children: <SolTransfer onSuccess={handleSuccess} onError={handleError} />,
    },
    {
      key: 'token',
      label: 'Token 转账',
      children: <TokenTransfer onSuccess={handleSuccess} onError={handleError} />,
    },
  ];

  return (
    <AppLayout>
      {contextHolder}
      <div style={{ padding: '24px' }}>
        <Card>
          <Tabs items={items} />
        </Card>
      </div>
    </AppLayout>
  );
};

export default SendPage;