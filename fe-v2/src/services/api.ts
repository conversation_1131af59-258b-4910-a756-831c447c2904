const API_BASE_URL = 'http://localhost:3001';

export class ApiService {
  private static baseUrl = API_BASE_URL;

  private static async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`API request failed: ${endpoint}`, error);
      throw error;
    }
  }

  // ==================== 系统接口 ====================

  static async healthCheck() {
    return this.request<{ status: string; timestamp: string }>('/health');
  }

  static async getBlockhash() {
    return this.request<{ blockhash: string }>('/api/blockhash');
  }

  // ==================== 账户信息接口 ====================

  static async getMultisigs() {
    return this.request<{ multisigs: any[] }>('/api/multisigs');
  }

  static async getBalance(publicKey: string) {
    return this.request<{ balance: number }>('/api/account', {
      method: 'GET',
      body: JSON.stringify({ publicKey }),
    });
  }

  // ==================== 兼容性方法 ====================
  // 保留一些常用的旧方法名以保持向后兼容

  /**
   * @deprecated 使用 getBlockhash 替代
   */
  static async getLatestBlockhash() {
    return this.getBlockhash();
  }
}

export default ApiService;