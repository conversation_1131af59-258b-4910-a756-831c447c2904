# API 迁移指南

## 🚀 新API结构概览

新的API设计更加简洁和一致，按功能模块组织，减少了接口数量并提高了易用性。

## 📋 接口变更对照表

### 系统接口
| 旧接口 | 新接口 | 说明 |
|--------|--------|------|
| `/api/solana/latest-blockhash` | `/api/blockhash` | 简化路径 |
| `/health` | `/health` | 无变化 |

### 账户信息接口
| 旧接口 | 新接口 | 说明 |
|--------|--------|------|
| `/api/multisig/accounts` | `/api/account` | 合并到统一账户接口 |
| `/api/multisig/details` | `/api/account` | 合并到统一账户接口 |
| `/api/multisig/get-vault-info` | `/api/account` | 合并到统一账户接口 |
| `/api/solana/balance` | `/api/account` | 合并到统一账户接口 |
| `/api/solana/token-balance` | `/api/account` | **合并到统一账户接口** |

### 转账接口
| 旧接口 | 新接口 | 说明 |
|--------|--------|------|
| `/api/multisig/create-sol-transfer` | `/api/transfer` | 合并SOL和Token转账 |
| `/api/multisig/create-token-transfer` | `/api/transfer` | 合并SOL和Token转账 |

### 交易管理接口
| 旧接口 | 新接口 | 说明 |
|--------|--------|------|
| `/api/multisig/proposals` | `/api/transactions` | 更准确的命名 |
| `/api/multisig/vote` | `/api/transactions/vote` | RESTful风格 |
| `/api/multisig/execute` | `/api/transactions/execute` | RESTful风格 |
| `/api/multisig/build-execute-instruction` | `/api/transactions/build-execute` | 简化路径 |

## 🔧 前端API调用变更

### 1. 获取账户信息

**旧方式：**
```typescript
// 分别调用多个接口
const multisigs = await apiService.getMultisigAccounts(publicKey);
const details = await apiService.getMultisigDetails(multisigAddress);
const vaultInfo = await apiService.getVaultInfo(multisigAddress);
const balance = await apiService.getBalance(publicKey);
const tokenBalance = await apiService.getTokenBalance(tokenAccount);
```

**新方式：**
```typescript
// 统一接口，按需获取
const accountInfo = await apiService.getAccountInfo({
  publicKey: publicKey,
  multisigAddress: multisigAddress,
  tokenAccount: tokenAccount  // 新增：Token余额查询
});

// 或者使用便捷方法（内部调用统一接口）
const multisigs = await apiService.getMultisigAccounts(publicKey);
const multisigDetails = await apiService.getMultisigDetails(multisigAddress);
const balance = await apiService.getBalance(publicKey);
const tokenBalance = await apiService.getTokenBalance(tokenAccount);
```

### 2. 创建转账

**旧方式：**
```typescript
// SOL转账
const solResult = await apiService.createSolTransfer({
  multisigAddress,
  recipientAddress,
  amount,
  creatorPublicKey,
  signedTransaction
});

// Token转账
const tokenResult = await apiService.createTokenTransfer({
  multisigAddress,
  recipientAddress,
  tokenMint,
  amount,
  decimals,
  creatorPublicKey,
  signedTransaction
});
```

**新方式：**
```typescript
// 统一转账接口
const result = await apiService.createTransfer({
  type: 'sol', // 或 'token'
  multisigAddress,
  recipientAddress,
  amount,
  creatorPublicKey,
  signedTransaction,
  // Token转账时需要
  tokenMint,
  decimals
});

// 或者使用便捷方法
const solResult = await apiService.createSolTransfer({...});
const tokenResult = await apiService.createTokenTransfer({...});
```

### 3. 交易管理

**旧方式：**
```typescript
const proposals = await apiService.getProposals(multisigAddress);

await apiService.submitVote({
  multisigAddress,
  transactionIndex,
  vote: 'approve',
  voterPublicKey,
  signedTransaction
});

await apiService.executeProposal({
  multisigAddress,
  transactionIndex,
  executorPublicKey,
  signedTransaction
});
```

**新方式：**
```typescript
const transactions = await apiService.getTransactions(multisigAddress);

await apiService.submitVote({
  multisigAddress,
  transactionIndex,
  userPublicKey, // 统一命名
  vote: 'approve',
  signedTransaction
});

await apiService.executeTransaction({
  multisigAddress,
  transactionIndex,
  userPublicKey, // 统一命名
  signedTransaction
});
```

## 📝 类型定义变更

### 主要类型更新
- `Proposal` → `Transaction`
- `ProposalsResponse` → `TransactionsResponse`
- `voterPublicKey/executorPublicKey` → `userPublicKey`
- 新增统一的 `TransferRequest` 和 `TransferResponse`
- **新增 `tokenAccount` 参数到 `AccountInfoRequest`**
- **新增 `tokenBalance` 字段到 `AccountInfoResponse`**

### 响应格式改进
- 统一的错误处理格式
- 更详细的成功消息
- 一致的字段命名
- **Token余额信息集成到账户信息响应中**

## 🔄 兼容性

为了保持向后兼容，我们保留了旧的方法名作为 `@deprecated` 方法：

```typescript
// 这些方法仍然可用，但建议迁移到新方法
apiService.getVaultInfo() // → apiService.getMultisigDetails()
apiService.getProposals() // → apiService.getTransactions()
apiService.executeProposal() // → apiService.executeTransaction()
apiService.getLatestBlockhash() // → apiService.getBlockhash()

// Token余额查询现在内部使用统一接口
apiService.getTokenBalance(tokenAccount) // 内部调用 /api/account
```

## ✅ 迁移检查清单

- [ ] 更新所有 `getProposals()` 调用为 `getTransactions()`
- [ ] 更新所有 `executeProposal()` 调用为 `executeTransaction()`
- [ ] 统一使用 `userPublicKey` 而不是 `voterPublicKey/executorPublicKey`
- [ ] 考虑使用统一的 `getAccountInfo()` 接口
- [ ] 考虑使用统一的 `createTransfer()` 接口
- [ ] 更新类型定义：`Proposal` → `Transaction`
- [ ] **Token余额查询现在通过统一账户接口处理**
- [ ] 测试所有API调用确保正常工作

## 🎯 优势总结

1. **接口数量减少**: 从16个减少到7个接口（再减少1个）
2. **一致性提升**: 统一的命名和响应格式
3. **易用性改进**: 更直观的接口设计
4. **维护性增强**: 模块化的代码组织
5. **扩展性更好**: 为未来功能预留空间
6. **更高效**: 一次请求可获取多种账户信息

## 🆕 最新更新

### Token余额查询优化
- **合并接口**: Token余额查询已合并到 `/api/account` 接口
- **参数扩展**: `AccountInfoRequest` 新增 `tokenAccount` 参数
- **响应扩展**: `AccountInfoResponse` 新增 `tokenBalance` 字段
- **向后兼容**: `getTokenBalance()` 方法保持不变，内部使用新接口

### 使用示例
```typescript
// 一次请求获取多种信息
const accountInfo = await apiService.getAccountInfo({
  publicKey: userWallet,           // 获取SOL余额和多签列表
  multisigAddress: selectedMultisig, // 获取多签详情
  tokenAccount: tokenAccountAddress  // 获取Token余额
});

// 结果包含所有信息
console.log(accountInfo.balance);        // SOL余额
console.log(accountInfo.multisigs);      // 多签列表
console.log(accountInfo.multisig);       // 多签详情
console.log(accountInfo.tokenBalance);   // Token余额
```