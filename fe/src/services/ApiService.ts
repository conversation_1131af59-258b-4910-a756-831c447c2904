const API_BASE_URL = 'http://localhost:3001';

// ==================== 基础类型定义 ====================

export interface MultisigAccount {
  address: string;
  members: number;
  threshold: number;
  transactionIndex: number;
}

export interface MultisigMember {
  key: string;
  permissions: any;
}

export interface VaultInfo {
  address: string;
  balance: number;
  balanceSOL: number;
}

export interface MultisigInfo {
  address: string;
  members: MultisigMember[];
  threshold: number;
  transactionIndex: number;
  createKey: string;
  allowExternalExecute: boolean;
  rentCollector?: string;
  vault: VaultInfo;
}

export interface Transaction {
  transactionIndex: number;
  status: 'Draft' | 'Active' | 'Approved' | 'Rejected' | 'Executed' | 'Cancelled';
  approvals: number;
  threshold: number;
  creator: string;
  memo?: string;
  votes: Array<{
    member: string;
    vote: 'Approve' | 'Reject';
  }>;
  canExecute: boolean;
}

export interface TokenBalance {
  balance: number;
  decimals: number;
  uiAmount: number;
}

// ==================== 请求/响应接口 ====================

export interface AccountInfoRequest {
  publicKey?: string;
  multisigAddress?: string;
  tokenAccount?: string;
}

export interface AccountInfoResponse {
  multisigs?: MultisigAccount[];
  balance?: number;
  multisig?: MultisigInfo;
  tokenBalance?: TokenBalance;
}

export interface TransferRequest {
  type: 'sol' | 'token';
  multisigAddress: string;
  recipientAddress: string;
  amount: string;
  creatorPublicKey: string;
  signedTransaction: string;
  // Token转账专用字段
  tokenMint?: string;
  decimals?: number;
}

export interface TransferResponse {
  signature: string;
  result?: any;
  success: boolean;
  message: string;
  confirmed?: boolean;
  note?: string;
  explorerUrl?: string;
}

export interface TransactionsResponse {
  transactions: Transaction[];
}

export interface TransactionActionRequest {
  multisigAddress: string;
  transactionIndex: number;
  userPublicKey: string;
  signedTransaction: string;
  vote?: 'approve' | 'reject'; // 投票时需要
}

export interface TransactionActionResponse {
  signature: string;
  result: any;
  success: boolean;
  message: string;
}

export interface BuildExecuteInstructionRequest {
  multisigAddress: string;
  transactionIndex: number;
  executorPublicKey: string;
}

export interface BuildExecuteInstructionResponse {
  instruction: {
    keys: Array<{
      pubkey: string;
      isSigner: boolean;
      isWritable: boolean;
    }>;
    programId: string;
    data: number[];
  };
  lookupTableAccounts: any[];
}

// ==================== API服务类 ====================

class ApiService {
  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ error: 'Network error' }));
      throw new Error(error.error || `HTTP ${response.status}`);
    }

    return response.json();
  }

  // ==================== 系统接口 ====================

  async healthCheck() {
    return this.request<{ status: string; timestamp: string }>('/health');
  }

  async getBlockhash() {
    return this.request<{ blockhash: string }>('/api/blockhash');
  }

  // ==================== 账户信息接口 ====================

  /**
   * 获取用户的多签账户列表
   */
  async getMultisigAccounts(publicKey: string): Promise<{ multisigs: MultisigAccount[] }> {
    const response = await this.request<AccountInfoResponse>('/api/account', {
      method: 'POST',
      body: JSON.stringify({ publicKey }),
    });
    return { multisigs: response.multisigs || [] };
  }

  /**
   * 获取多签详细信息（包含金库信息）
   */
  async getMultisigDetails(multisigAddress: string): Promise<MultisigInfo> {
    const response = await this.request<AccountInfoResponse>('/api/account', {
      method: 'POST',
      body: JSON.stringify({ multisigAddress }),
    });

    if (!response.multisig) {
      throw new Error('多签信息不存在');
    }

    return response.multisig;
  }

  /**
   * 获取完整账户信息（多签列表 + 指定多签详情 + Token余额）
   */
  async getAccountInfo(request: AccountInfoRequest): Promise<AccountInfoResponse> {
    return this.request<AccountInfoResponse>('/api/account', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  /**
   * 获取SOL余额
   */
  async getBalance(publicKey: string): Promise<{ balance: number }> {
    const response = await this.request<AccountInfoResponse>('/api/account', {
      method: 'POST',
      body: JSON.stringify({ publicKey }),
    });
    return { balance: response.balance || 0 };
  }

  /**
   * 获取Token余额
   */
  async getTokenBalance(tokenAccount: string): Promise<TokenBalance> {
    const response = await this.request<AccountInfoResponse>('/api/account', {
      method: 'POST',
      body: JSON.stringify({ tokenAccount }),
    });
    return response.tokenBalance || { balance: 0, decimals: 0, uiAmount: 0 };
  }

  // ==================== 转账接口 ====================

  /**
   * 创建SOL转账
   */
  async createSolTransfer(request: Omit<TransferRequest, 'type'>): Promise<TransferResponse> {
    return this.request<TransferResponse>('/api/transfer', {
      method: 'POST',
      body: JSON.stringify({ ...request, type: 'sol' }),
    });
  }

  /**
   * 创建Token转账
   */
  async createTokenTransfer(request: Omit<TransferRequest, 'type'> & { tokenMint: string; decimals: number }): Promise<TransferResponse> {
    return this.request<TransferResponse>('/api/transfer', {
      method: 'POST',
      body: JSON.stringify({ ...request, type: 'token' }),
    });
  }

  /**
   * 统一转账接口
   */
  async createTransfer(request: TransferRequest): Promise<TransferResponse> {
    return this.request<TransferResponse>('/api/transfer', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  // ==================== 交易管理接口 ====================

  /**
   * 获取交易列表
   */
  async getTransactions(multisigAddress: string): Promise<TransactionsResponse> {
    return this.request<TransactionsResponse>('/api/transactions', {
      method: 'POST',
      body: JSON.stringify({ multisigAddress }),
    });
  }

  /**
   * 提交投票
   */
  async submitVote(request: TransactionActionRequest & { vote: 'approve' | 'reject' }): Promise<TransactionActionResponse> {
    return this.request<TransactionActionResponse>('/api/transactions/vote', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  /**
   * 执行交易
   */
  async executeTransaction(request: Omit<TransactionActionRequest, 'vote'>): Promise<TransactionActionResponse> {
    return this.request<TransactionActionResponse>('/api/transactions/execute', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  /**
   * 构建执行指令
   */
  async buildExecuteInstruction(request: BuildExecuteInstructionRequest): Promise<BuildExecuteInstructionResponse> {
    return this.request<BuildExecuteInstructionResponse>('/api/transactions/build-execute', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  // ==================== 兼容性方法 ====================
  // 为了保持向后兼容，保留一些旧方法名

  /**
   * @deprecated 使用 getMultisigDetails 替代
   */
  async getVaultInfo(multisigAddress: string) {
    const multisig = await this.getMultisigDetails(multisigAddress);
    return {
      multisigInfo: {
        address: multisig.address,
        members: multisig.members,
        threshold: multisig.threshold,
        transactionIndex: multisig.transactionIndex,
      },
      vaultInfo: multisig.vault
    };
  }

  /**
   * @deprecated 使用 getTransactions 替代
   */
  async getProposals(multisigAddress: string) {
    const response = await this.getTransactions(multisigAddress);
    return { proposals: response.transactions };
  }

  /**
   * @deprecated 使用 executeTransaction 替代
   */
  async executeProposal(request: any) {
    return this.executeTransaction({
      multisigAddress: request.multisigAddress,
      transactionIndex: request.transactionIndex,
      userPublicKey: request.executorPublicKey,
      signedTransaction: request.signedTransaction,
    });
  }

  /**
   * @deprecated 使用 getBlockhash 替代
   */
  async getLatestBlockhash() {
    return this.getBlockhash();
  }
}

export const apiService = new ApiService();