import React from 'react';
import { ConnectionProvider, WalletProvider } from '@solana/wallet-adapter-react';
import { WalletAdapterNetwork } from '@solana/wallet-adapter-base';
import { WalletModalProvider } from '@solana/wallet-adapter-react-ui';
import { PhantomWalletAdapter } from '@solana/wallet-adapter-phantom';
import { clusterApiUrl } from '@solana/web3.js';
import Demo from './components/Demo';
import './App.css';

// 选择网络 (可以是 'devnet', 'testnet', 或 'mainnet-beta')
const network = WalletAdapterNetwork.Mainnet;

// 你也可以直接提供一个自定义的 RPC 端点
const endpoint = clusterApiUrl(network);

// 初始化钱包适配器
const wallets = [
  new PhantomWalletAdapter(),
];

function App() {
  return (
    <ConnectionProvider endpoint={endpoint}>
      <WalletProvider wallets={wallets} autoConnect>
        <WalletModalProvider>
          <div className="container">
            <h1>SQUADS</h1>
            <Demo />
          </div>
        </WalletModalProvider>
      </WalletProvider>
    </ConnectionProvider>
  );
}

export default App;