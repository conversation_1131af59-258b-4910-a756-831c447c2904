import React, { useState, useCallback } from 'react';
import { useWallet, useConnection } from '@solana/wallet-adapter-react';
import { PublicKey, SystemProgram, TransactionMessage, VersionedTransaction, LAMPORTS_PER_SOL } from '@solana/web3.js';
import * as multisig from '@sqds/multisig';
import { Send, AlertCircle, CheckCircle } from 'lucide-react';
import MultisigSelector from './MultisigSelector';
import { apiService } from '../services/ApiService';

interface SolTransferProps {
  onSuccess?: (signature: string) => void;
  onError?: (error: string) => void;
}

const SolTransfer: React.FC<SolTransferProps> = ({ onSuccess, onError }) => {
  const { publicKey, signTransaction, wallet } = useWallet();
  const { connection } = useConnection();
  const [amount, setAmount] = useState('0.0001');
  const [recipient, setRecipient] = useState('');
  const [selectedMultisig, setSelectedMultisig] = useState('');
  const [loading, setLoading] = useState(false);
  const [status, setStatus] = useState<string>('');
  const [transactionIndex, setTransactionIndex] = useState<string>('');

  // 默认配置
  const programId = new PublicKey("SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf");

  const validateInputs = () => {
    if (!publicKey) {
      throw new Error('请先连接钱包');
    }
    if (!selectedMultisig.trim()) {
      throw new Error('请选择多签账户');
    }
    if (!recipient.trim()) {
      throw new Error('请输入接收地址');
    }
    if (!amount || parseFloat(amount) <= 0) {
      throw new Error('请输入有效的转账金额');
    }

    // 验证地址格式
    try {
      new PublicKey(selectedMultisig);
    } catch {
      throw new Error('多签地址格式不正确');
    }

    try {
      new PublicKey(recipient);
    } catch {
      throw new Error('接收地址格式不正确');
    }
  };

  const handleCreateNew = () => {
    onError?.('创建新多签账户功能将在后续步骤中实现');
  };

  const handleTransfer = useCallback(async () => {
    if (!signTransaction) {
      onError?.('钱包不支持签名功能');
      return;
    }

    // 检查钱包是否支持版本化交易
    const supportedVersions = wallet?.adapter?.supportedTransactionVersions;
    if (!supportedVersions || !supportedVersions.has(0)) {
      onError?.('当前钱包不支持版本化交易(v0)，请更新钱包或使用支持版本化交易的钱包');
      return;
    }

    try {
      setLoading(true);
      setStatus('验证输入信息...');

      validateInputs();

      const multisigPda = new PublicKey(selectedMultisig);
      const recipientPubkey = new PublicKey(recipient);
      const lamports = Math.floor(parseFloat(amount) * LAMPORTS_PER_SOL);

      setStatus('获取多签信息...');

      // 使用后端API获取多签和金库信息
      const vaultInfo = await apiService.getVaultInfo(selectedMultisig);
      const { multisigInfo, vaultInfo: vault } = vaultInfo;

      console.log(`多签配置: ${multisigInfo.threshold}/${multisigInfo.members.length} 阈值`);

      // 检查当前用户是否是多签成员
      const isMember = multisigInfo.members.some(member => member.key === publicKey!.toBase58());
      if (!isMember) {
        throw new Error('当前钱包不是多签成员');
      }

      const vaultPda = new PublicKey(vault.address);
      const vaultBalance = vault.balance;

      setStatus(`金库余额: ${vault.balanceSOL.toFixed(6)} SOL`);

      if (vaultBalance < lamports) {
        throw new Error(`金库余额不足。当前余额: ${vault.balanceSOL.toFixed(6)} SOL`);
      }

      // 创建转账指令
      const transferInstruction = SystemProgram.transfer({
        fromPubkey: vaultPda,
        toPubkey: recipientPubkey,
        lamports,
      });

      setStatus('构建多签交易...');

      // 获取最新的区块哈希
      const { blockhash } = await apiService.getLatestBlockhash();

      // 创建转账消息
      const transferMessage = new TransactionMessage({
        payerKey: vaultPda,
        recentBlockhash: blockhash,
        instructions: [transferInstruction],
      });

      // 使用简化的单一交易方式
      const nextTransactionIndex = BigInt(multisigInfo.transactionIndex) + 1n;
      setTransactionIndex(nextTransactionIndex.toString());

      console.log('创建多签交易指令...');

      // 直接使用前端 SDK 构建单个交易，包含所有必要指令
      const createIx = multisig.instructions.vaultTransactionCreate({
        multisigPda,
        transactionIndex: nextTransactionIndex,
        creator: publicKey!,
        vaultIndex: 0,
        ephemeralSigners: 0,
        transactionMessage: transferMessage,
        memo: `Transfer ${amount} SOL to ${recipient.substring(0, 8)}...`,
        programId,
      });

      const proposalIx = multisig.instructions.proposalCreate({
        multisigPda,
        transactionIndex: nextTransactionIndex,
        creator: publicKey!,
        isDraft: false,
        programId,
      });

      const approveIx = multisig.instructions.proposalApprove({
        multisigPda,
        transactionIndex: nextTransactionIndex,
        member: publicKey!,
        programId,
      });

      console.log('构建单一合并交易...');

      // 创建单个包含所有指令的交易
      const combinedMessage = new TransactionMessage({
        payerKey: publicKey!,
        recentBlockhash: blockhash,
        instructions: [createIx, proposalIx, approveIx],
      });

      const combinedTx = new VersionedTransaction(combinedMessage.compileToV0Message());

      console.log('交易构建完成，大小:', combinedTx.serialize().length, 'bytes');
      console.log('钱包支持的交易版本:', Array.from(supportedVersions));

      setStatus('请在钱包中签名交易...');
      console.log('请求钱包签名...');

      try {
        const signedTx = await signTransaction(combinedTx);
        console.log('钱包签名成功');

        // 保存签名交易数据用于调试
        const signedTxBase64 = Buffer.from(signedTx.serialize()).toString('base64');
        console.log('=== 调试信息 ===');
        console.log('签名交易 Base64:', signedTxBase64);
        console.log('交易大小:', signedTx.serialize().length, 'bytes');
        console.log('多签地址:', selectedMultisig);
        console.log('接收地址:', recipient);
        console.log('转账金额:', amount, 'SOL');
        console.log('创建者:', publicKey!.toBase58());

        // 保存到localStorage用于测试
        localStorage.setItem('debugSignedTransaction', JSON.stringify({
          signedTransaction: signedTxBase64,
          multisigAddress: selectedMultisig,
          recipientAddress: recipient,
          amount: amount,
          creatorPublicKey: publicKey!.toBase58(),
          timestamp: new Date().toISOString()
        }));
        console.log('调试数据已保存到 localStorage.debugSignedTransaction');
        console.log('===============');

        setStatus('提交交易...');

        // 使用后端API发送交易
        const result = await apiService.createSolTransfer({
          multisigAddress: selectedMultisig,
          recipientAddress: recipient,
          amount: amount,
          creatorPublicKey: publicKey!.toBase58(),
          signedTransaction: signedTxBase64
        });

        console.log('交易提交成功:', result.signature);
        setStatus('SOL转账提案创建并批准成功！');

        onSuccess?.(result.signature);

      } catch (signError: any) {
        console.error('交易签名失败详细信息:', signError);

        if (signError.message?.includes('Transaction simulation failed') ||
            signError.message?.includes('模拟过程中已被撤销')) {
          throw new Error('交易模拟失败，可能原因：\n1. 账户余额不足支付交易费用\n2. 转账金额过大\n3. 网络拥堵导致交易失败\n\n建议：减少转账金额或稍后重试');
        }

        // 检查是否是版本化交易支持问题
        if (signError.message?.includes('version') ||
            signError.message?.includes('Versioned') ||
            signError.message?.includes('not supported')) {
          throw new Error('钱包不支持当前交易版本，请更新钱包到最新版本或使用其他钱包');
        }

        throw signError;
      }

    } catch (error: any) {
      console.error('转账失败:', error);
      setStatus('');
      onError?.(error.message || '转账失败');
    } finally {
      setLoading(false);
    }
  }, [publicKey, signTransaction, connection, recipient, selectedMultisig, amount, onSuccess, onError, wallet]);

  return (
    <div>
      <MultisigSelector
        selectedMultisig={selectedMultisig}
        onSelect={setSelectedMultisig}
        onCreateNew={handleCreateNew}
      />

      <div style={{ marginBottom: '16px' }}>
        <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
          接收地址:
        </label>
        <input
          type="text"
          className="input"
          value={recipient}
          onChange={(e) => setRecipient(e.target.value)}
          placeholder="输入接收地址"
          disabled={loading}
        />
      </div>

      <div style={{ marginBottom: '16px' }}>
        <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
          转账金额 (SOL):
        </label>
        <input
          type="number"
          className="input"
          value={amount}
          onChange={(e) => setAmount(e.target.value)}
          placeholder="输入转账金额"
          step="0.0001"
          min="0.0001"
          disabled={loading}
        />
      </div>

      <button
        className="button"
        onClick={handleTransfer}
        disabled={loading || !publicKey || !selectedMultisig}
        style={{
          background: loading || !selectedMultisig ? '#ccc' : '#007bff',
          cursor: loading || !selectedMultisig ? 'not-allowed' : 'pointer',
          width: '100%',
          marginBottom: '16px'
        }}
      >
        {loading ? '处理中...' : '创建SOL转账'}
      </button>

      {status && (
        <div style={{
          padding: '12px',
          background: '#f0f8ff',
          borderRadius: '6px',
          color: '#007bff',
          marginBottom: '16px',
          display: 'flex',
          alignItems: 'center',
          gap: '8px'
        }}>
          {loading ? (
            <AlertCircle size={16} />
          ) : (
            <CheckCircle size={16} />
          )}
          {status}
        </div>
      )}

      {transactionIndex && (
        <div style={{
          padding: '12px',
          background: '#f8f9fa',
          borderRadius: '6px',
          marginBottom: '16px'
        }}>
          <p style={{ color: '#666', fontSize: '14px' }}>
            <strong>交易索引:</strong> {transactionIndex}
          </p>
          <p style={{ color: '#666', fontSize: '12px', marginTop: '4px' }}>
            其他成员可以使用此索引进行投票和执行
          </p>
        </div>
      )}

      <div style={{ fontSize: '14px', color: '#666' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' }}>
          <AlertCircle size={16} />
          <strong>重要说明:</strong>
        </div>
        <ul style={{ paddingLeft: '20px', lineHeight: '1.5' }}>
          <li>建议先用小额测试</li>
          <li>需要达到多签阈值后才能执行转账</li>
          <li>此功能创建多签转账交易并进行第一次投票</li>
          <li>请确保在正确的网络上操作 (当前: Mainnet)</li>
        </ul>
      </div>
    </div>
  );
};

export default SolTransfer;