import React, { useState, useCallback } from 'react';
import { useWallet, useConnection } from '@solana/wallet-adapter-react';
import { PublicKey, TransactionMessage, VersionedTransaction } from '@solana/web3.js';
import { TOKEN_PROGRAM_ID, getAssociatedTokenAddress, createTransferInstruction } from '@solana/spl-token';
import * as multisig from '@sqds/multisig';
import { Send, AlertCircle, CheckCircle, Coins } from 'lucide-react';
import MultisigSelector from './MultisigSelector';
import { apiService } from '../services/ApiService';

interface TokenTransferProps {
  onSuccess?: (signature: string) => void;
  onError?: (error: string) => void;
}

const TokenTransfer: React.FC<TokenTransferProps> = ({ onSuccess, onError }) => {
  const { publicKey, signTransaction } = useWallet();
  const { connection } = useConnection();
  const [amount, setAmount] = useState('0.0001');
  const [recipient, setRecipient] = useState('');
  const [tokenMint, setTokenMint] = useState('');
  const [decimals, setDecimals] = useState(6);
  const [selectedMultisig, setSelectedMultisig] = useState('');
  const [loading, setLoading] = useState(false);
  const [status, setStatus] = useState<string>('');
  const [transactionIndex, setTransactionIndex] = useState<string>('');

  // 默认配置
  const programId = new PublicKey("SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf");

  // 常用Token地址（主网）
  const commonTokens = [
    { name: 'USDC', mint: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', decimals: 6 },
  ];

  const validateInputs = () => {
    if (!publicKey) {
      throw new Error('请先连接钱包');
    }
    if (!selectedMultisig.trim()) {
      throw new Error('请选择多签账户');
    }
    if (!recipient.trim()) {
      throw new Error('请输入接收地址');
    }
    if (!tokenMint.trim()) {
      throw new Error('请输入Token合约地址');
    }
    if (!amount || parseFloat(amount) <= 0) {
      throw new Error('请输入有效的转账金额');
    }
    if (decimals < 0 || decimals > 18) {
      throw new Error('小数位数必须在0-18之间');
    }

    // 验证地址格式
    try {
      new PublicKey(selectedMultisig);
    } catch {
      throw new Error('多签地址格式不正确');
    }

    try {
      new PublicKey(recipient);
    } catch {
      throw new Error('接收地址格式不正确');
    }

    try {
      new PublicKey(tokenMint);
    } catch {
      throw new Error('Token合约地址格式不正确');
    }
  };

  const handleCreateNew = () => {
    onError?.('创建新多签账户功能将在后续步骤中实现');
  };

  const handleTokenSelect = (token: { name: string; mint: string; decimals: number }) => {
    setTokenMint(token.mint);
    setDecimals(token.decimals);
  };

  const handleTransfer = useCallback(async () => {
    if (!signTransaction) {
      onError?.('钱包不支持签名功能');
      return;
    }

    try {
      setLoading(true);
      setStatus('验证输入信息...');

      validateInputs();

      const multisigPda = new PublicKey(selectedMultisig);
      const recipientPubkey = new PublicKey(recipient);
      const tokenMintPubkey = new PublicKey(tokenMint);
      const transferAmount = Math.floor(parseFloat(amount) * Math.pow(10, decimals));

      setStatus('获取多签信息...');

      // 使用后端API获取多签和金库信息
      const vaultInfo = await apiService.getVaultInfo(selectedMultisig);
      const { multisigInfo, vaultInfo: vault } = vaultInfo;

      console.log(`多签配置: ${multisigInfo.threshold}/${multisigInfo.members.length} 阈值`);

      // 检查当前用户是否是多签成员
      const isMember = multisigInfo.members.some(member => member.key === publicKey!.toBase58());
      if (!isMember) {
        throw new Error('当前钱包不是多签成员');
      }

      const vaultPda = new PublicKey(vault.address);

      setStatus('获取Token账户信息...');

      // 获取金库和接收者的Token账户地址
      const vaultTokenAccount = await getAssociatedTokenAddress(
        tokenMintPubkey,
        vaultPda,
        true // allowOwnerOffCurve
      );

      const recipientTokenAccount = await getAssociatedTokenAddress(
        tokenMintPubkey,
        recipientPubkey
      );

      // 检查金库Token账户余额
      setStatus('检查Token余额...');
      const vaultTokenBalance = await apiService.getTokenBalance(vaultTokenAccount.toBase58());

      setStatus(`Token余额: ${(vaultTokenBalance.balance / Math.pow(10, decimals)).toFixed(decimals)} Token`);

      if (vaultTokenBalance.balance < transferAmount) {
        throw new Error(`Token余额不足。当前余额: ${(vaultTokenBalance.balance / Math.pow(10, decimals)).toFixed(decimals)} Token`);
      }

      // 创建Token转账指令
      const transferInstruction = createTransferInstruction(
        vaultTokenAccount,
        recipientTokenAccount,
        vaultPda,
        transferAmount,
        [],
        TOKEN_PROGRAM_ID
      );

      const { blockhash } = await apiService.getLatestBlockhash();
      const transferMessage = new TransactionMessage({
        payerKey: vaultPda,
        recentBlockhash: blockhash,
        instructions: [transferInstruction],
      });

      const nextTransactionIndex = BigInt(multisigInfo.transactionIndex) + 1n;
      setTransactionIndex(nextTransactionIndex.toString());

      setStatus('创建多签交易...');

      // 创建交易并投票
      const createIx = multisig.instructions.vaultTransactionCreate({
        multisigPda,
        transactionIndex: nextTransactionIndex,
        creator: publicKey!,
        vaultIndex: 0,
        ephemeralSigners: 0,
        transactionMessage: transferMessage,
        memo: `Transfer ${amount} Token to ${recipient.substring(0, 8)}...`,
        programId,
      });

      const proposalIx = multisig.instructions.proposalCreate({
        multisigPda,
        transactionIndex: nextTransactionIndex,
        creator: publicKey!,
        isDraft: false,
        programId,
      });

      const approveIx = multisig.instructions.proposalApprove({
        multisigPda,
        transactionIndex: nextTransactionIndex,
        member: publicKey!,
        programId,
      });

      const combinedMessage = new TransactionMessage({
        payerKey: publicKey!,
        recentBlockhash: blockhash,
        instructions: [createIx, proposalIx, approveIx],
      });

      const combinedTx = new VersionedTransaction(combinedMessage.compileToV0Message());

      setStatus('请在钱包中签名...');
      const signedTx = await signTransaction(combinedTx);

      setStatus('提交交易...');

      // 使用后端API发送交易
      const result = await apiService.createTokenTransfer({
        multisigAddress: selectedMultisig,
        recipientAddress: recipient,
        tokenMint: tokenMint,
        amount: amount,
        decimals: decimals,
        creatorPublicKey: publicKey!.toBase58(),
        signedTransaction: Buffer.from(signedTx.serialize()).toString('base64')
      });

      setStatus('Token转账交易创建成功！等待其他成员投票...');
      onSuccess?.(result.signature);

    } catch (error: any) {
      console.error('Token转账失败:', error);
      setStatus('');
      onError?.(error.message || 'Token转账失败');
    } finally {
      setLoading(false);
    }
  }, [publicKey, signTransaction, recipient, selectedMultisig, tokenMint, amount, decimals, onSuccess, onError]);

  return (
    <div>
      <MultisigSelector
        selectedMultisig={selectedMultisig}
        onSelect={setSelectedMultisig}
        onCreateNew={handleCreateNew}
      />

      <div style={{ marginBottom: '16px' }}>
        <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
          选择Token类型:
        </label>
        <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap', marginBottom: '8px' }}>
          {commonTokens.map((token) => (
            <button
              key={token.mint}
              onClick={() => handleTokenSelect(token)}
              style={{
                padding: '6px 12px',
                border: tokenMint === token.mint ? '2px solid #007bff' : '1px solid #ccc',
                borderRadius: '4px',
                background: tokenMint === token.mint ? '#f0f8ff' : 'white',
                cursor: 'pointer',
                fontSize: '12px'
              }}
              disabled={loading}
            >
              {token.name}
            </button>
          ))}
        </div>
        <input
          type="text"
          className="input"
          value={tokenMint}
          onChange={(e) => setTokenMint(e.target.value)}
          placeholder="或输入Token合约地址"
          disabled={loading}
        />
      </div>

      <div style={{ marginBottom: '16px' }}>
        <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
          小数位数:
        </label>
        <input
          type="number"
          className="input"
          value={decimals}
          onChange={(e) => setDecimals(parseInt(e.target.value) || 0)}
          placeholder="Token小数位数"
          min="0"
          max="18"
          disabled={loading}
        />
      </div>

      <div style={{ marginBottom: '16px' }}>
        <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
          接收地址:
        </label>
        <input
          type="text"
          className="input"
          value={recipient}
          onChange={(e) => setRecipient(e.target.value)}
          placeholder="输入接收地址"
          disabled={loading}
        />
      </div>

      <div style={{ marginBottom: '16px' }}>
        <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
          转账金额:
        </label>
        <input
          type="number"
          className="input"
          value={amount}
          onChange={(e) => setAmount(e.target.value)}
          placeholder="输入转账金额"
          step="0.000001"
          min="0.000001"
          disabled={loading}
        />
      </div>

      <button
        className="button"
        onClick={handleTransfer}
        disabled={loading || !publicKey || !selectedMultisig}
        style={{
          background: loading || !selectedMultisig ? '#ccc' : '#007bff',
          cursor: loading || !selectedMultisig ? 'not-allowed' : 'pointer',
          width: '100%',
          marginBottom: '16px'
        }}
      >
        {loading ? '处理中...' : '创建Token转账'}
      </button>

      {status && (
        <div style={{
          padding: '12px',
          background: '#f0f8ff',
          borderRadius: '6px',
          color: '#007bff',
          marginBottom: '16px',
          display: 'flex',
          alignItems: 'center',
          gap: '8px'
        }}>
          {loading ? (
            <AlertCircle size={16} />
          ) : (
            <CheckCircle size={16} />
          )}
          {status}
        </div>
      )}

      {transactionIndex && (
        <div style={{
          padding: '12px',
          background: '#f8f9fa',
          borderRadius: '6px',
          marginBottom: '16px'
        }}>
          <p style={{ color: '#666', fontSize: '14px' }}>
            <strong>交易索引:</strong> {transactionIndex}
          </p>
          <p style={{ color: '#666', fontSize: '12px', marginTop: '4px' }}>
            其他成员可以使用此索引进行投票和执行
          </p>
        </div>
      )}

      <div style={{ fontSize: '14px', color: '#666' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' }}>
          <Coins size={16} />
          <strong>Token转账说明:</strong>
        </div>
        <ul style={{ paddingLeft: '20px', lineHeight: '1.5' }}>
          <li>支持所有SPL Token转账</li>
          <li>需要确保金库有足够的Token余额</li>
          <li>接收地址必须有对应的Token账户</li>
          <li>请确认Token合约地址和小数位数正确</li>
          <li>建议先用小额测试</li>
        </ul>
      </div>
    </div>
  );
};

export default TokenTransfer;