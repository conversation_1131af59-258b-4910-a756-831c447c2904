{"name": "squads-fe", "version": "1.0.0", "description": "Squads Frontend", "main": "index.js", "type": "module", "scripts": {"start": "vite", "build": "vite build"}, "dependencies": {"buffer": "^6.0.3", "react": "^18.2.0", "react-dom": "^18.2.0", "lucide-react": "^0.263.1", "@sqds/multisig": "^2.1.0", "@solana/web3.js": "^1.87.6", "@solana/spl-token": "^0.4.0", "@solana/wallet-adapter-base": "^0.9.27", "@solana/wallet-adapter-phantom": "^0.9.28", "@solana/wallet-adapter-react": "^0.15.35", "@solana/wallet-adapter-react-ui": "^0.9.35"}, "devDependencies": {"vite": "^4.5.3", "typescript": "^5.2.2", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1"}, "engines": {"node": ">=16.0.0"}}