import Guide from '@/components/Guide';
import { trim } from '@/utils/format';
import { PageContainer } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import styles from './index.less';
import GlobalHeader from '@/components/Header';
import TimeDisplay from '@/components/TimeDisplay';

const HomePage: React.FC = () => {
  const { name } = useModel('global');
  return (
    <PageContainer ghost>
      <div className={styles.container}>
        <Guide name={trim(name)} />
        <div style={{ marginTop: '20px', padding: '20px', background: '#fff' }}>
          <h2>时间显示测试</h2>
          <GlobalHeader />
          <div style={{ marginTop: '20px', padding: '10px', border: '1px solid #eee' }}>
            <h3>独立时间组件测试：</h3>
            <TimeDisplay />
          </div>
        </div>
      </div>
    </PageContainer>
  );
};

export default HomePage;
