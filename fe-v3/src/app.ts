// 运行时配置
import React from 'react';
import RightContent from '@/components/RightContent';

// 全局初始化数据配置，用于 Layout 用户信息和权限初始化
// 更多信息见文档：https://umijs.org/docs/api/runtime-config#getinitialstate
export async function getInitialState(): Promise<{ name: string }> {
  return { name: 'Squads Network' };
}

export const layout = () => {
  return {
    title: 'Squads',
    logo: false,
    menu: {
      locale: false,
    },
    contentStyle: { padding: 0, margin: 0 },
    rightContentRender: false,
  };
};

// 自定义内容渲染
export function rootContainer(container: React.ReactElement) {
  return React.createElement(RightContent, {}, container);
}
