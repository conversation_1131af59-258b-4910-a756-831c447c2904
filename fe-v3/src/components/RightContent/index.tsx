import React from 'react';
import { Layout } from 'antd';
import GlobalHeader from '../Header';
import styles from './index.less';

const { Content } = Layout;

interface RightContentProps {
  children: React.ReactNode;
}

const RightContent: React.FC<RightContentProps> = ({ children }) => {
  const rightLayoutStyle = {
    height: '100%',
    background: '#f0f2f5'
  };

  const contentStyle = {
    margin: '16px',
    padding: '16px',
    background: '#fff',
    borderRadius: '4px'
  };

  return (
    <Layout className={styles.rightLayout} style={rightLayoutStyle}>
      <GlobalHeader />
      <Content className={styles.content} style={contentStyle}>
        {children}
      </Content>
    </Layout>
  );
};

export default RightContent;