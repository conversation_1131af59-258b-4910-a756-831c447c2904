import React from 'react';
import { Layout } from 'antd';
import TimeDisplay from '../TimeDisplay';
import styles from './index.less';

const { Header } = Layout;

const GlobalHeader: React.FC = () => {
  const headerStyle = {
    height: '48px',
    lineHeight: '48px',
    background: '#fff',
    boxShadow: '0 1px 4px rgba(0, 21, 41, 0.08)',
    padding: '0',
    display: 'flex',
    alignItems: 'center'
  };

  const contentStyle = {
    display: 'flex',
    justifyContent: 'flex-end',
    width: '100%',
    padding: '0 16px'
  };

  const rightStyle = {
    display: 'flex',
    alignItems: 'center'
  };

  return (
    <Header className={styles.header} style={headerStyle}>
      <div className={styles.content} style={contentStyle}>
        <div className={styles.right} style={rightStyle}>
          <TimeDisplay />
        </div>
      </div>
    </Header>
  );
};

export default GlobalHeader;