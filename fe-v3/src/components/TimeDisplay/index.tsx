import React, { useState, useEffect } from 'react';
import { Typography, Space } from 'antd';
import { ClockCircleOutlined } from '@ant-design/icons';

const { Text } = Typography;

const TimeDisplay: React.FC = () => {
  const [currentTime, setCurrentTime] = useState<string>('');

  useEffect(() => {
    // 更新当前时间的函数
    const updateTime = () => {
      const now = new Date();
      const timeString = now.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
      });
      setCurrentTime(timeString);
    };

    // 初始化时间
    updateTime();

    // 每秒更新一次时间
    const timer = setInterval(updateTime, 1000);

    // 组件卸载时清除定时器
    return () => clearInterval(timer);
  }, []);

  const timeStyle = {
    fontSize: '16px',
    fontWeight: 'bold',
    color: '#1890ff'
  };

  return (
    <Space>
      <ClockCircleOutlined />
      <Text style={timeStyle}>{currentTime || '加载中...'}</Text>
    </Space>
  );
};

export default TimeDisplay;