const Koa = require('koa');
const cors = require('koa-cors');
const bodyParser = require('koa-bodyparser');
const router = require('./router');
const { config, validateConfig, printConfig } = require('./config');

// 验证配置
validateConfig();
printConfig();

const app = new Koa();

// 中间件
app.use(cors({
  origin: config.cors.origin,
  credentials: config.cors.credentials,
  'Access-Control-Allow-Origin': true,
  'Access-Control-Allow-Methods': true,
  'Access-Control-Allow-Headers': true,
}));

app.use(bodyParser());

// 路由
app.use(router.routes());
app.use(router.allowedMethods());

// 启动服务器
app.listen(config.server.port, () => {
  console.log(`🚀 Squads Backend Server running on http://localhost:${config.server.port}`);
  console.log(`🌐 Environment: ${config.server.nodeEnv}`);
  console.log(`🔗 Solana Network: ${config.solana.network}`);
});

// 错误处理
app.on('error', (err, ctx) => {
  console.error('Server error:', err);
});