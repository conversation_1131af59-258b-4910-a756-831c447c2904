# Squads Backend V2

## 概述

Squads Backend V2 是一个基于 Koa.js 的 Node.js 后端服务，用于管理 Solana 多签钱包操作。

## 配置管理

### 环境变量配置

所有配置都通过环境变量进行管理，支持 `.env` 文件。

#### 配置文件

1. **`.env`** - 实际环境变量文件（不提交到版本控制）
2. **`.env.example`** - 配置模板文件
3. **`config.js`** - 配置管理模块

#### 配置项说明

##### 服务器配置
- `PORT`: 服务器端口号（默认: 3001）
- `NODE_ENV`: 运行环境（默认: development）

##### Solana 网络配置
- `SOLANA_NETWORK`: Solana 网络类型（默认: mainnet-beta）
- `SOLANA_RPC_URL`: Solana RPC 节点地址
- `SOLANA_COMMITMENT`: 交易确认级别（默认: confirmed）

##### Multisig 程序配置
- `MULTISIG_PROGRAM_ID`: Squads 多签程序 ID
- `KNOWN_MULTISIG_ADDRESSES`: 已知多签地址列表（逗号分隔）

##### 交易配置
- `TX_TIMEOUT`: 交易确认超时时间（毫秒，默认: 15000）
- `MAX_PROPOSALS_TO_CHECK`: 最大检查提案数量（默认: 20）
- `TX_MAX_RETRIES`: 交易重试次数（默认: 3）

##### CORS 配置
- `CORS_ORIGIN`: 允许的跨域来源（默认: *）
- `CORS_CREDENTIALS`: 是否允许携带凭证（默认: true）

### 使用方法

1. **复制配置模板**
   ```bash
   cp .env.example .env
   ```

2. **修改配置**
   编辑 `.env` 文件，根据实际需求修改配置项。

3. **启动服务**
   ```bash
   npm start
   ```

### 配置验证

服务启动时会自动验证所有配置项：
- 端口号范围检查
- RPC URL 格式验证
- Solana 公钥格式验证
- 超时时间合理性检查

如果配置验证失败，服务将拒绝启动并显示详细错误信息。

### 配置示例

```env
# 开发环境配置
PORT=3001
NODE_ENV=development
SOLANA_NETWORK=devnet
SOLANA_RPC_URL=https://api.devnet.solana.com
KNOWN_MULTISIG_ADDRESSES=YourDevnetMultisigAddress

# 生产环境配置
PORT=3001
NODE_ENV=production
SOLANA_NETWORK=mainnet-beta
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
KNOWN_MULTISIG_ADDRESSES=HVx9YZh7ZNiNzBkRVyoGc3PxsznCJyv3PPz57fEKVuFr
```

## API 接口

### 系统接口

- `GET /health` - 健康检查
- `GET /api/blockhash` - 获取最新区块哈希

### 账户接口

- `POST /api/account` - 获取账户信息

### 转账接口

- `POST /api/transfer` - 创建转账交易

### 交易管理接口

- `POST /api/transactions` - 获取交易列表
- `POST /api/transactions/build-execute` - 构建执行指令
- `POST /api/transactions/:action` - 交易操作（投票/执行）

## 开发

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm start
```

### 项目结构

```
be-v2/
├── .env.example          # 配置模板
├── config.js            # 配置管理
├── server.js            # 服务器入口
├── router.js            # 路由定义
├── utils.js             # 工具函数
└── README.md           # 项目文档
```

## 注意事项

1. **安全性**: 不要将 `.env` 文件提交到版本控制系统
2. **配置验证**: 服务启动前会进行完整的配置验证
3. **环境隔离**: 不同环境使用不同的配置文件
4. **多签地址**: 确保 `KNOWN_MULTISIG_ADDRESSES` 中的地址格式正确
