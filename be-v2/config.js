require('dotenv').config();
const { PublicKey } = require('@solana/web3.js');

/**
 * 应用配置管理
 * 所有配置项都集中在这里管理
 */
const config = {
  // 服务器配置
  server: {
    port: parseInt(process.env.PORT) || 3001,
    nodeEnv: process.env.NODE_ENV || 'development',
  },

  // Solana 网络配置
  solana: {
    network: process.env.SOLANA_NETWORK || 'mainnet-beta',
    rpcUrl: process.env.SOLANA_RPC_URL || 'https://api.mainnet-beta.solana.com',
    commitment: process.env.SOLANA_COMMITMENT || 'confirmed',
  },

  // Multisig 程序配置
  multisig: {
    programId: new PublicKey(process.env.MULTISIG_PROGRAM_ID || 'SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf'),
    knownAddresses: process.env.KNOWN_MULTISIG_ADDRESSES 
      ? process.env.KNOWN_MULTISIG_ADDRESSES.split(',').map(addr => addr.trim())
      : ['HVx9YZh7ZNiNzBkRVyoGc3PxsznCJyv3PPz57fEKVuFr'],
  },

  // 交易配置
  transaction: {
    timeout: parseInt(process.env.TX_TIMEOUT) || 15000,
    maxProposalsToCheck: parseInt(process.env.MAX_PROPOSALS_TO_CHECK) || 20,
    maxRetries: parseInt(process.env.TX_MAX_RETRIES) || 3,
  },

  // CORS 配置
  cors: {
    origin: process.env.CORS_ORIGIN || '*',
    credentials: process.env.CORS_CREDENTIALS === 'true' || true,
  },
};

// 配置验证函数
const validateConfig = () => {
  const errors = [];

  // 验证端口号
  if (isNaN(config.server.port) || config.server.port < 1 || config.server.port > 65535) {
    errors.push('Invalid PORT: must be a number between 1 and 65535');
  }

  // 验证 RPC URL
  try {
    new URL(config.solana.rpcUrl);
  } catch (error) {
    errors.push('Invalid SOLANA_RPC_URL: must be a valid URL');
  }

  // 验证 Multisig Program ID
  try {
    new PublicKey(process.env.MULTISIG_PROGRAM_ID || 'SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf');
  } catch (error) {
    errors.push('Invalid MULTISIG_PROGRAM_ID: must be a valid Solana public key');
  }

  // 验证已知多签地址
  config.multisig.knownAddresses.forEach((address, index) => {
    try {
      new PublicKey(address);
    } catch (error) {
      errors.push(`Invalid KNOWN_MULTISIG_ADDRESSES[${index}]: ${address} is not a valid Solana public key`);
    }
  });

  // 验证超时时间
  if (isNaN(config.transaction.timeout) || config.transaction.timeout < 1000) {
    errors.push('Invalid TX_TIMEOUT: must be a number >= 1000 (milliseconds)');
  }

  if (errors.length > 0) {
    console.error('❌ Configuration validation failed:');
    errors.forEach(error => console.error(`  - ${error}`));
    process.exit(1);
  }

  console.log('✅ Configuration validation passed');
};

// 打印配置信息（隐藏敏感信息）
const printConfig = () => {
  console.log('📋 Application Configuration:');
  console.log(`  Server: ${config.server.nodeEnv} mode on port ${config.server.port}`);
  console.log(`  Solana: ${config.solana.network} (${config.solana.rpcUrl})`);
  console.log(`  Multisig Program: ${config.multisig.programId.toString()}`);
  console.log(`  Known Addresses: ${config.multisig.knownAddresses.length} configured`);
  console.log(`  Transaction Timeout: ${config.transaction.timeout}ms`);
};

// 导出配置和工具函数
module.exports = {
  config,
  validateConfig,
  printConfig,
};
