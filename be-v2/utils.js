require('dotenv').config();
const { Connection, PublicKey } = require('@solana/web3.js');

// 从环境变量获取常量
const MULTISIG_PROGRAM_ID = new PublicKey(process.env.MULTISIG_PROGRAM_ID);
const connection = new Connection(process.env.SOLANA_RPC_URL, process.env.SOLANA_COMMITMENT);
const KNOWN_MULTISIG_ADDRESSES = process.env.KNOWN_MULTISIG_ADDRESSES.split(',').map(addr => addr.trim());
const TX_TIMEOUT = parseInt(process.env.TX_TIMEOUT);
const MAX_PROPOSALS_TO_CHECK = parseInt(process.env.MAX_PROPOSALS_TO_CHECK);

// 工具函数
const validateParams = (params, required) => {
  const missing = required.filter(key => !params[key]);
  return missing.length ? `缺少参数: ${missing.join(', ')}` : null;
};

const handleError = (ctx, error, message) => {
  const status = error.status || 500;
  ctx.status = status;
  ctx.body = { error: message || error.message };
};

const sendTransaction = async (signedTransaction, options = {}) => {
  const signature = await connection.sendRawTransaction(
    Buffer.from(signedTransaction, 'base64'),
    {
      skipPreflight: true,
      preflightCommitment: process.env.SOLANA_COMMITMENT,
      maxRetries: parseInt(process.env.TX_MAX_RETRIES),
      ...options
    }
  );
  return signature;
};

const confirmTransactionWithTimeout = async (signature, timeout = TX_TIMEOUT) => {
  try {
    const result = await Promise.race([
      connection.confirmTransaction(signature, process.env.SOLANA_COMMITMENT),
      new Promise((_, reject) => setTimeout(() => reject(new Error('确认超时')), timeout))
    ]);
    return { confirmed: true, result };
  } catch (error) {
    return { confirmed: false, error: error.message };
  }
};

module.exports = {
  MULTISIG_PROGRAM_ID,
  connection,
  KNOWN_MULTISIG_ADDRESSES,
  TX_TIMEOUT,
  MAX_PROPOSALS_TO_CHECK,
  validateParams,
  handleError,
  sendTransaction,
  confirmTransactionWithTimeout
};