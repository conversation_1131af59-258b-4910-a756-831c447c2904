const { Connection, PublicKey } = require('@solana/web3.js');
const { config } = require('./config');

// 从配置文件获取常量
const MULTISIG_PROGRAM_ID = config.multisig.programId;
const connection = new Connection(config.solana.rpcUrl, config.solana.commitment);
const KNOWN_MULTISIG_ADDRESSES = config.multisig.knownAddresses;
const TX_TIMEOUT = config.transaction.timeout;
const MAX_PROPOSALS_TO_CHECK = config.transaction.maxProposalsToCheck;

// 工具函数
const validateParams = (params, required) => {
  const missing = required.filter(key => !params[key]);
  return missing.length ? `缺少参数: ${missing.join(', ')}` : null;
};

const handleError = (ctx, error, message) => {
  const status = error.status || 500;
  ctx.status = status;
  ctx.body = { error: message || error.message };
};

const sendTransaction = async (signedTransaction, options = {}) => {
  const signature = await connection.sendRawTransaction(
    Buffer.from(signedTransaction, 'base64'),
    {
      skipPreflight: true,
      preflightCommitment: config.solana.commitment,
      maxRetries: config.transaction.maxRetries,
      ...options
    }
  );
  return signature;
};

const confirmTransactionWithTimeout = async (signature, timeout = TX_TIMEOUT) => {
  try {
    const result = await Promise.race([
      connection.confirmTransaction(signature, config.solana.commitment),
      new Promise((_, reject) => setTimeout(() => reject(new Error('确认超时')), timeout))
    ]);
    return { confirmed: true, result };
  } catch (error) {
    return { confirmed: false, error: error.message };
  }
};

module.exports = {
  MULTISIG_PROGRAM_ID,
  connection,
  KNOWN_MULTISIG_ADDRESSES,
  TX_TIMEOUT,
  MAX_PROPOSALS_TO_CHECK,
  validateParams,
  handleError,
  sendTransaction,
  confirmTransactionWithTimeout
};