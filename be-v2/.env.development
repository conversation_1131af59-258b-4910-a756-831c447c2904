# 开发环境配置
PORT=3001
NODE_ENV=development

# Solana 网络配置 - 开发网
SOLANA_NETWORK=devnet
SOLANA_RPC_URL=https://api.devnet.solana.com
SOLANA_COMMITMENT=confirmed

# Multisig 程序配置
MULTISIG_PROGRAM_ID=SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf

# 开发环境多签地址（示例）
KNOWN_MULTISIG_ADDRESSES=YourDevnetMultisigAddress1,YourDevnetMultisigAddress2

# 交易配置 - 开发环境可以设置更短的超时时间
TX_TIMEOUT=10000
MAX_PROPOSALS_TO_CHECK=10
TX_MAX_RETRIES=2

# CORS 配置 - 开发环境允许所有来源
CORS_ORIGIN=*
CORS_CREDENTIALS=true
