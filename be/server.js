const Koa = require('koa');
const cors = require('koa-cors');
const bodyParser = require('koa-bodyparser');
const router = require('./router');

const app = new Koa();

// 中间件
app.use(cors({
  origin: '*',
  credentials: true,
  'Access-Control-Allow-Origin': true,
  'Access-Control-Allow-Methods': true,
  'Access-Control-Allow-Headers': true,
}));

app.use(bodyParser());

// 路由
app.use(router.routes());
app.use(router.allowedMethods());

// 启动服务器
const PORT = process.env.PORT || 3001;

app.listen(PORT, () => {
  console.log(`🚀 Squads Backend Server running on http://localhost:${PORT}`);
});

// 错误处理
app.on('error', (err, ctx) => {
  console.error('Server error:', err);
});