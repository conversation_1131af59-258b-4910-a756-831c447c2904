const { Connection, PublicKey } = require('@solana/web3.js');

// 常量
const MULTISIG_PROGRAM_ID = new PublicKey("SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf");
const connection = new Connection('https://api.mainnet-beta.solana.com', 'confirmed');
const KNOWN_MULTISIG_ADDRESSES = ['HVx9YZh7ZNiNzBkRVyoGc3PxsznCJyv3PPz57fEKVuFr'];
const TX_TIMEOUT = 15000;
const MAX_PROPOSALS_TO_CHECK = 20;

// 工具函数
const validateParams = (params, required) => {
  const missing = required.filter(key => !params[key]);
  return missing.length ? `缺少参数: ${missing.join(', ')}` : null;
};

const handleError = (ctx, error, message) => {
  const status = error.status || 500;
  ctx.status = status;
  ctx.body = { error: message || error.message };
};

const sendTransaction = async (signedTransaction, options = {}) => {
  const signature = await connection.sendRawTransaction(
    Buffer.from(signedTransaction, 'base64'),
    { skipPreflight: true, preflightCommitment: 'confirmed', maxRetries: 3, ...options }
  );
  return signature;
};

const confirmTransactionWithTimeout = async (signature, timeout = TX_TIMEOUT) => {
  try {
    const result = await Promise.race([
      connection.confirmTransaction(signature, 'confirmed'),
      new Promise((_, reject) => setTimeout(() => reject(new Error('确认超时')), timeout))
    ]);
    return { confirmed: true, result };
  } catch (error) {
    return { confirmed: false, error: error.message };
  }
};

module.exports = {
  MULTISIG_PROGRAM_ID,
  connection,
  KNOWN_MULTISIG_ADDRESSES,
  TX_TIMEOUT,
  MAX_PROPOSALS_TO_CHECK,
  validateParams,
  handleError,
  sendTransaction,
  confirmTransactionWithTimeout
};